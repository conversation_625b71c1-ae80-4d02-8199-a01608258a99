<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_dispute_list" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\item_dispute_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_dispute_list_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="35"/></Target><Target id="@+id/cvStatusBadge" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="22" startOffset="8" endLine="44" endOffset="43"/></Target><Target id="@+id/tvStatus" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="32" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/ivMailIcon" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="47" startOffset="8" endLine="55" endOffset="54"/></Target><Target id="@+id/tvSender" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="57" startOffset="8" endLine="71" endOffset="34"/></Target><Target id="@+id/tvReceiver" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="73" startOffset="8" endLine="88" endOffset="34"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="91" startOffset="8" endLine="100" endOffset="56"/></Target><Target id="@+id/tvDateTime" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="103" startOffset="8" endLine="114" endOffset="56"/></Target></Targets></Layout>