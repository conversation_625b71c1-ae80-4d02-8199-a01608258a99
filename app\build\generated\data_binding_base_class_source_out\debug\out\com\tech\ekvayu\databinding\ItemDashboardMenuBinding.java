// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDashboardMenuBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cvIconBackground;

  @NonNull
  public final CardView cvMain;

  @NonNull
  public final AppCompatImageView ivIcon;

  @NonNull
  public final AppCompatTextView tvDescripton;

  @NonNull
  public final AppCompatTextView tvMenuName;

  private ItemDashboardMenuBinding(@NonNull CardView rootView, @NonNull CardView cvIconBackground,
      @NonNull CardView cvMain, @NonNull AppCompatImageView ivIcon,
      @NonNull AppCompatTextView tvDescripton, @NonNull AppCompatTextView tvMenuName) {
    this.rootView = rootView;
    this.cvIconBackground = cvIconBackground;
    this.cvMain = cvMain;
    this.ivIcon = ivIcon;
    this.tvDescripton = tvDescripton;
    this.tvMenuName = tvMenuName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDashboardMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDashboardMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_dashboard_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDashboardMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cvIconBackground;
      CardView cvIconBackground = ViewBindings.findChildViewById(rootView, id);
      if (cvIconBackground == null) {
        break missingId;
      }

      CardView cvMain = (CardView) rootView;

      id = R.id.ivIcon;
      AppCompatImageView ivIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivIcon == null) {
        break missingId;
      }

      id = R.id.tvDescripton;
      AppCompatTextView tvDescripton = ViewBindings.findChildViewById(rootView, id);
      if (tvDescripton == null) {
        break missingId;
      }

      id = R.id.tvMenuName;
      AppCompatTextView tvMenuName = ViewBindings.findChildViewById(rootView, id);
      if (tvMenuName == null) {
        break missingId;
      }

      return new ItemDashboardMenuBinding((CardView) rootView, cvIconBackground, cvMain, ivIcon,
          tvDescripton, tvMenuName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
