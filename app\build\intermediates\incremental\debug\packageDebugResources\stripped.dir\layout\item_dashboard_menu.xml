<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cvMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardUseCompatPadding="true"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_12sdp"
    android:elevation="@dimen/_4sdp"
    app:cardElevation="@dimen/_4sdp"
    app:cardBackgroundColor="@color/card_background"
    android:foreground="?attr/selectableItemBackground"
    xmlns:app="http://schemas.android.com/apk/res-auto">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_120sdp"
            android:padding="@dimen/_14sdp">

            <!-- Icon with background circle -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cvIconBackground"
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                app:cardCornerRadius="@dimen/_20sdp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="@color/surface"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivIcon"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="center"
                    android:scaleType="centerInside"
                    android:src="@drawable/activity"
                    android:tint="@color/text_primary" />

            </androidx.cardview.widget.CardView>

            <!-- Menu Title -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMenuName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8sdp"
                android:text="@string/device_details"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/_12sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintTop_toBottomOf="@+id/cvIconBackground"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Menu Description -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDescripton"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_4sdp"
                android:text="View your device-specific information"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/_8sdp"
                android:fontFamily="@font/roboto_semi_regular"
                android:maxLines="3"
                android:ellipsize="end"
                android:gravity="top"
                android:lineSpacingExtra="@dimen/_1sdp"
                app:layout_constraintTop_toBottomOf="@+id/tvMenuName"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>