<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_device_details" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_device_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_device_details_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="743" endOffset="12"/></Target><Target id="@+id/tvTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="18" startOffset="8" endLine="27" endOffset="59"/></Target><Target id="@+id/cvDeviceInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="30" startOffset="8" endLine="221" endOffset="43"/></Target><Target id="@+id/tvDeviceName" view="TextView"><Expressions/><location startLine="74" startOffset="20" endLine="82" endOffset="47"/></Target><Target id="@+id/tvManufacturer" view="TextView"><Expressions/><location startLine="108" startOffset="20" endLine="116" endOffset="47"/></Target><Target id="@+id/tvModelName" view="TextView"><Expressions/><location startLine="142" startOffset="20" endLine="150" endOffset="47"/></Target><Target id="@+id/tvBradName" view="TextView"><Expressions/><location startLine="176" startOffset="20" endLine="184" endOffset="47"/></Target><Target id="@+id/tvProductName" view="TextView"><Expressions/><location startLine="209" startOffset="20" endLine="217" endOffset="47"/></Target><Target id="@+id/cvAndroidInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="224" startOffset="8" endLine="415" endOffset="43"/></Target><Target id="@+id/tvAndroidVersion" view="TextView"><Expressions/><location startLine="268" startOffset="20" endLine="276" endOffset="47"/></Target><Target id="@+id/tvApiLevel" view="TextView"><Expressions/><location startLine="302" startOffset="20" endLine="310" endOffset="47"/></Target><Target id="@+id/tvBuildNumber" view="TextView"><Expressions/><location startLine="336" startOffset="20" endLine="344" endOffset="47"/></Target><Target id="@+id/tvSerialNumber" view="TextView"><Expressions/><location startLine="370" startOffset="20" endLine="378" endOffset="47"/></Target><Target id="@+id/tvAndroidId" view="TextView"><Expressions/><location startLine="403" startOffset="20" endLine="411" endOffset="47"/></Target><Target id="@+id/cvScreenInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="418" startOffset="8" endLine="575" endOffset="43"/></Target><Target id="@+id/tvResolution" view="TextView"><Expressions/><location startLine="462" startOffset="20" endLine="470" endOffset="47"/></Target><Target id="@+id/tvDensity" view="TextView"><Expressions/><location startLine="496" startOffset="20" endLine="504" endOffset="47"/></Target><Target id="@+id/tvDensityFactor" view="TextView"><Expressions/><location startLine="530" startOffset="20" endLine="538" endOffset="47"/></Target><Target id="@+id/tvScaleDensity" view="TextView"><Expressions/><location startLine="563" startOffset="20" endLine="571" endOffset="47"/></Target><Target id="@+id/cvAppInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="578" startOffset="8" endLine="737" endOffset="43"/></Target><Target id="@+id/tvPackage" view="TextView"><Expressions/><location startLine="624" startOffset="20" endLine="632" endOffset="47"/></Target><Target id="@+id/tvVersion" view="TextView"><Expressions/><location startLine="658" startOffset="20" endLine="666" endOffset="47"/></Target><Target id="@+id/tvTargetSdk" view="TextView"><Expressions/><location startLine="692" startOffset="20" endLine="700" endOffset="47"/></Target><Target id="@+id/tvMinSdk" view="TextView"><Expressions/><location startLine="725" startOffset="20" endLine="733" endOffset="47"/></Target></Targets></Layout>