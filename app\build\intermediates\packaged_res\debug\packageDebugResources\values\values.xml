<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="app_color">#FF000080</color>
    <color name="app_color_dark">#FF000060</color>
    <color name="app_color_light">#FF4040B0</color>
    <color name="background">@color/background_light</color>
    <color name="background_dark">#121212</color>
    <color name="background_light">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="blue">#0066CC</color>
    <color name="border">@color/border_light</color>
    <color name="border_dark">#505050</color>
    <color name="border_light">#DEE2E6</color>
    <color name="card_background">@color/card_background_light</color>
    <color name="card_background_dark">#2D2D2D</color>
    <color name="card_background_light">#FFFFFF</color>
    <color name="dark_grey">#6B6A6A</color>
    <color name="divider">@color/divider_light</color>
    <color name="divider_dark">#404040</color>
    <color name="divider_light">#E9ECEF</color>
    <color name="error">#DC3545</color>
    <color name="error_light">#F8D7DA</color>
    <color name="green">#046A08</color>
    <color name="grey">#DCDCDC</color>
    <color name="ic_launcher_background">#FFFFFF</color>
    <color name="info">#17A2B8</color>
    <color name="info_light">#D1ECF1</color>
    <color name="light_grey">#E9E9E9</color>
    <color name="orange">#FF8C00</color>
    <color name="red">#FF1100</color>
    <color name="success">#28A745</color>
    <color name="success_light">#D4EDDA</color>
    <color name="surface">@color/surface_light</color>
    <color name="surface_dark">#1E1E1E</color>
    <color name="surface_light">#F8F9FA</color>
    <color name="text_primary">@color/text_primary_light</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_primary_light">#212529</color>
    <color name="text_secondary">@color/text_secondary_light</color>
    <color name="text_secondary_dark">#B3B3B3</color>
    <color name="text_secondary_light">#6C757D</color>
    <color name="transparent">#00000000</color>
    <color name="transparent_black">#CC000000</color>
    <color name="transparent_black_60">#99000000</color>
    <color name="warning">#FFC107</color>
    <color name="warning_light">#FFF3CD</color>
    <color name="white">#FFFFFFFF</color>
    <integer name="google_play_services_version">12451000</integer>
    <string name="accessibility_service_gmail">Monitors for mail in Gmail.</string>
    <string name="accessibility_service_outlook">Monitors for mail in outlook.</string>
    <string name="accessibility_service_yahoo">Monitors for mail in yahoo.</string>
    <string name="android_id">Android Id</string>
    <string name="android_info">Android Info</string>
    <string name="android_version">Android Version</string>
    <string name="api_level">Api Level</string>
    <string name="appAuthRedirectScheme">https://ekvayuy.firebaseapp.com/__/auth/handler</string>
    <string name="app_info">App Info</string>
    <string name="app_name">Ekvayu</string>
    <string name="basic_information">Basic Information</string>
    <string name="brand_name">Brand Name</string>
    <string name="build_number">Build Number</string>
    <string name="close">Close</string>
    <string name="continuee">Continue</string>
    <string name="counter">Counter</string>
    <string name="default_web_client_id" translatable="false">206367517092-4ujn198g73oklcf96ql11pd4kh65d3c9.apps.googleusercontent.com</string>
    <string name="density_factor">Density Factor</string>
    <string name="device_details">Device Details</string>
    <string name="device_name">Device Name</string>
    <string name="gcm_defaultSenderId" translatable="false">206367517092</string>
    <string name="get_stared_raised_disputes_against">Get Started! Raised disputes against</string>
    <string name="gmail_auth">Gmail Auth</string>
    <string name="google_api_key" translatable="false">AIzaSyAYJyBc1w4EMC7KoLsoKncEK4evTPzK4SU</string>
    <string name="google_app_id" translatable="false">1:206367517092:android:2b4d293336c444f9fa7931</string>
    <string name="google_client_id">408481275944-9clep0h2rardm0f07ugg6f765ik6bpnb.apps.googleusercontent.com</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAYJyBc1w4EMC7KoLsoKncEK4evTPzK4SU</string>
    <string name="google_storage_bucket" translatable="false">ekvayuy.firebasestorage.app</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="i_am_facing_difficulties_retrieving_the_recipient_s_email_id_could_you_kindly_enable_the_service_and_assist_in_verifying_the_mail_for_a_timely_resolution">I am facing difficulties retrieving the recipient\'s email ID. Could you kindly enable the service and assist in verifying the mail for a timely resolution?</string>
    <string name="manufacturer">Manufacturer</string>
    <string name="manufacturer_name">Manufacturer Name</string>
    <string name="message_id">Message Id</string>
    <string name="min_sdk">Min SDK</string>
    <string name="model_name">Model Name</string>
    <string name="permission">Permission</string>
    <string name="product">Product</string>
    <string name="project_id" translatable="false">ekvayuy</string>
    <string name="raise_dispute">Raise Dispute</string>
    <string name="reason_of_raising_dispute">Reason Of Raising Dispute</string>
    <string name="refresh">Refresh</string>
    <string name="resolution">Resolution</string>
    <string name="safe">safe</string>
    <string name="scaled_density">Scaled Density</string>
    <string name="screen_info">Screen Info</string>
    <string name="secure_my_app_from_phishing">Shield your inbox from phishing, malicious emails, and unauthorized access with robust security 🔒 🛡️ 🔐 🔑 measures to keep your personal information safe and secure.</string>
    <string name="sender_email">Sender Email</string>
    <string name="serial_number">Serial Number</string>
    <string name="status">Status</string>
    <string name="submit">Submit</string>
    <string name="to_extract_email_details_securely">To extract email details securely, we need Accessibility Service permission. This helps us identify and analyze email content directly from your mail app to keep you safe. No personal data is stored or shared.</string>
    <string name="unsafe">unsafe</string>
    <string name="version">Version</string>
    <string name="warning_this_email_is_unsafe_proceed_with_caution">Warning: This email is unsafe! Proceed with caution.</string>
    <string name="yahoo_auth">Yahoo Auth</string>
    <style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/app_color</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">true</item>

        
        <item name="android:windowBackground">@color/background</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style>
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style>
    <style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="HeaderButtonStyle">
        <item name="cardBackgroundColor">@color/app_color</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style>
    <style name="HeaderStyle">
        <item name="android:background">@color/surface</item>
        <item name="android:elevation">4dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>
    <style name="HeaderThemeButtonStyle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style>
    <style name="HeaderTitleStyle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style>
    <style name="Theme.Ekvayu" parent="Base.Theme.Ekvayu"/>
</resources>