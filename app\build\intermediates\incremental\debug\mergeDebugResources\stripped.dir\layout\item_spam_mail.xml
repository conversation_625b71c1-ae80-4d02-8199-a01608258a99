<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_12sdp"
    android:elevation="@dimen/_3sdp"
    app:cardElevation="@dimen/_3sdp"
    android:layout_marginHorizontal="@dimen/_12sdp"
    android:layout_marginVertical="@dimen/_6sdp"
    app:cardBackgroundColor="@color/card_background">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_14sdp">

        <!-- Mail Icon -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMailIcon"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:src="@drawable/ic_email"
            android:tint="@color/text_secondary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Sender Email -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:fontFamily="@font/roboto_semi_medium"
            android:textSize="@dimen/_12sdp"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            app:layout_constraintEnd_toStartOf="@+id/cvStatusBadge"
            app:layout_constraintTop_toTopOf="@+id/ivMailIcon"
            android:text="<EMAIL>" />

        <!-- Status Badge -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvStatusBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/_10sdp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/red"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivMailIcon">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unsafe"
                android:textColor="@color/white"
                android:textSize="@dimen/_9sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:paddingHorizontal="@dimen/_8sdp"
                android:paddingVertical="@dimen/_4sdp" />

        </androidx.cardview.widget.CardView>

        <!-- Receiver Email -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvReceiver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:fontFamily="@font/roboto_semi_regular"
            android:textSize="@dimen/_10sdp"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            app:layout_constraintEnd_toStartOf="@+id/cvStatusBadge"
            app:layout_constraintTop_toBottomOf="@+id/tvSender"
            android:text="To: <EMAIL>" />

        <!-- Divider -->
        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_8sdp"
            android:background="@color/divider"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvReceiver"
            android:layout_marginStart="@dimen/_10sdp" />

        <!-- Date/Time -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDateTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:fontFamily="@font/roboto_semi_regular"
            android:textSize="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_6sdp"
            android:layout_marginStart="@dimen/_10sdp"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            app:layout_constraintTop_toBottomOf="@+id/divider"
            android:text="Recently" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>
