<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_12sdp"
    android:elevation="@dimen/_3sdp"
    app:cardElevation="@dimen/_3sdp"
    android:layout_marginHorizontal="@dimen/_12sdp"
    android:layout_marginVertical="@dimen/_6sdp"
    app:cardBackgroundColor="@color/card_background">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16sdp">

        <!-- Status Badge -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvStatusBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="@dimen/_8sdp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pending"
                android:paddingHorizontal="@dimen/_12sdp"
                android:paddingVertical="@dimen/_4sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/_10sdp"
                android:gravity="center" />

        </androidx.cardview.widget.CardView>

        <!-- Mail Icon -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMailIcon"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:src="@drawable/ic_email"
            android:tint="@color/app_color"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginEnd="@dimen/_12sdp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/_12sdp"
            app:layout_constraintEnd_toStartOf="@+id/cvStatusBadge"
            android:fontFamily="@font/roboto_semi_medium"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginStart="@dimen/_12sdp"
            android:text="<EMAIL>"
            android:ellipsize="end"
            android:maxLines="1" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvReceiver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/_11sdp"
            app:layout_constraintEnd_toStartOf="@+id/cvStatusBadge"
            android:fontFamily="@font/roboto_semi_regular"
            app:layout_constraintTop_toBottomOf="@+id/tvSender"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_4sdp"
            android:layout_marginStart="@dimen/_12sdp"
            android:text="<EMAIL>"
            android:ellipsize="end"
            android:maxLines="1" />

        <!-- Divider Line -->
        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/divider"
            app:layout_constraintTop_toBottomOf="@+id/tvReceiver"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/_8sdp"
            android:layout_marginStart="@dimen/_12sdp" />

        <!-- Date/Time Text -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDateTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2 hours ago"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/_9sdp"
            android:fontFamily="@font/roboto_semi_regular"
            app:layout_constraintTop_toBottomOf="@+id/divider"
            app:layout_constraintStart_toEndOf="@+id/ivMailIcon"
            android:layout_marginTop="@dimen/_6sdp"
            android:layout_marginStart="@dimen/_12sdp" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>
