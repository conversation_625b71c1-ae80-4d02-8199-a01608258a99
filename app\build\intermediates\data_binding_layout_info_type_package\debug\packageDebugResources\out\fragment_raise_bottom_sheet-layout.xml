<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_raise_bottom_sheet" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_raise_bottom_sheet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_raise_bottom_sheet_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="334" endOffset="12"/></Target><Target id="@+id/clMain" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="8" startOffset="4" endLine="327" endOffset="55"/></Target><Target id="@+id/ivDisputeLogo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="19" startOffset="8" endLine="29" endOffset="13"/></Target><Target id="@+id/tvDisputeHeading" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="32" startOffset="8" endLine="42" endOffset="13"/></Target><Target id="@+id/cvMessageId" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="44" startOffset="8" endLine="91" endOffset="43"/></Target><Target id="@+id/tvMessageId" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="64" startOffset="16" endLine="72" endOffset="21"/></Target><Target id="@+id/etMessageId" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="75" startOffset="16" endLine="85" endOffset="21"/></Target><Target id="@+id/cvSenderEmail" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="94" startOffset="8" endLine="141" endOffset="43"/></Target><Target id="@+id/tvTitleSenderEmail" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="114" startOffset="16" endLine="122" endOffset="21"/></Target><Target id="@+id/etSenderMail" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="125" startOffset="16" endLine="135" endOffset="21"/></Target><Target id="@+id/llMainPending" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="144" startOffset="8" endLine="248" endOffset="54"/></Target><Target id="@+id/cvStatus" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="153" startOffset="12" endLine="198" endOffset="47"/></Target><Target id="@+id/tvStatusTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="172" startOffset="20" endLine="180" endOffset="25"/></Target><Target id="@+id/etStatus" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="183" startOffset="20" endLine="193" endOffset="25"/></Target><Target id="@+id/cvCounter" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="200" startOffset="12" endLine="245" endOffset="47"/></Target><Target id="@+id/tvTitleStatus" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="219" startOffset="20" endLine="227" endOffset="25"/></Target><Target id="@+id/etCounter" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="230" startOffset="20" endLine="240" endOffset="25"/></Target><Target id="@+id/cvReason" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="251" startOffset="8" endLine="297" endOffset="43"/></Target><Target id="@+id/tvTitleReason" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="271" startOffset="16" endLine="279" endOffset="21"/></Target><Target id="@+id/etReason" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="282" startOffset="16" endLine="292" endOffset="21"/></Target><Target id="@+id/btSubmit" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="300" startOffset="8" endLine="321" endOffset="13"/></Target></Targets></Layout>