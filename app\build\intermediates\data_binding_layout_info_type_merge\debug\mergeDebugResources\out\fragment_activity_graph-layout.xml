<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_activity_graph" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_activity_graph.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/fragment_activity_graph_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="488" endOffset="12"/></Target><Target id="@+id/cvHeader" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="25" startOffset="8" endLine="86" endOffset="43"/></Target><Target id="@+id/tvHeader" view="TextView"><Expressions/><location startLine="43" startOffset="16" endLine="51" endOffset="45"/></Target><Target id="@+id/tvSubTitle" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="63" endOffset="61"/></Target><Target id="@+id/cvChart" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="90" startOffset="8" endLine="125" endOffset="43"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="118" startOffset="16" endLine="122" endOffset="66"/></Target><Target id="@+id/cvStats" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="128" startOffset="8" endLine="280" endOffset="43"/></Target><Target id="@+id/summaryCards" view="LinearLayout"><Expressions/><location startLine="157" startOffset="16" endLine="278" endOffset="30"/></Target><Target id="@+id/tvDispute" view="TextView"><Expressions/><location startLine="181" startOffset="28" endLine="188" endOffset="77"/></Target><Target id="@+id/tvSpam" view="TextView"><Expressions/><location startLine="219" startOffset="28" endLine="226" endOffset="77"/></Target><Target id="@+id/tvProcessed" view="TextView"><Expressions/><location startLine="257" startOffset="28" endLine="264" endOffset="77"/></Target><Target id="@+id/cvInsights" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="283" startOffset="8" endLine="362" endOffset="43"/></Target><Target id="@+id/cvPerformance" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="365" startOffset="8" endLine="484" endOffset="43"/></Target></Targets></Layout>