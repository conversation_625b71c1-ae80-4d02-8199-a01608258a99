/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder android.app.Application kotlin.EnumB Acom.google.android.material.bottomsheet.BottomSheetDialogFragmentB Acom.google.android.material.bottomsheet.BottomSheetDialogFragmentB Acom.google.android.material.bottomsheet.BottomSheetDialogFragmentB Acom.google.android.material.bottomsheet.BottomSheetDialogFragment2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService androidx.fragment.app.Fragment androidx.fragment.app.Fragmenti androidx.fragment.app.FragmentIcom.tech.ekvayu.Adapter.DisputeListAdapter.OnDisputeMailListClickListener` <EMAIL>.onClickEventListner_ androidx.fragment.app.Fragment?com.tech.ekvayu.Adapter.SpamMailAdapter.OnSpamMailClickListener!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding2 1android.accessibilityservice.AccessibilityService) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivityB Acom.google.android.material.bottomsheet.BottomSheetDialogFragment2 1android.accessibilityservice.AccessibilityService androidx.fragment.app.Fragment` <EMAIL>.onClickEventListner_ androidx.fragment.app.Fragment?com.tech.ekvayu.Adapter.SpamMailAdapter.OnSpamMailClickListener) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService