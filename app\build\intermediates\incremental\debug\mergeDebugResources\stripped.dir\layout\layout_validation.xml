<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/_26sdp"
    android:orientation="vertical">



    <androidx.cardview.widget.CardView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_20sdp"
        android:elevation="@dimen/_20sdp"
        app:cardCornerRadius="@dimen/_15sdp"
        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_20sdp"
            android:layout_gravity="center"
            android:background="@color/card_background"
            >

            <com.airbnb.lottie.LottieAnimationView
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:id="@+id/lvWarning"
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_50sdp"
                app:lottie_rawRes="@raw/wait"
                app:lottie_autoPlay="true"
                android:scaleType="fitCenter"
                app:lottie_loop="true"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="@dimen/_15sdp"
                android:text="@string/warning_this_email_is_unsafe_proceed_with_caution"
                android:textSize="@dimen/_10sdp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textColor="@android:color/black"
                android:gravity="center"
                android:maxLines="3"
                android:ellipsize="end"
                android:layout_gravity="center"
                app:layout_constraintTop_toBottomOf="@+id/lvWarning"
                app:layout_constraintBottom_toTopOf="@+id/btClose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btClose"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/close"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_8sdp"
                android:textAlignment="center"
                android:layout_marginStart="@dimen/_2sdp"
                android:paddingVertical="@dimen/_6sdp"
                android:elevation="@dimen/_10sdp"
                android:fontFamily="@font/roboto_semi_medium"
                android:background="@drawable/bg_button_white"
                android:backgroundTint="@color/app_color"
                />







        </androidx.constraintlayout.widget.ConstraintLayout>








    </androidx.cardview.widget.CardView>







</androidx.constraintlayout.widget.ConstraintLayout>
