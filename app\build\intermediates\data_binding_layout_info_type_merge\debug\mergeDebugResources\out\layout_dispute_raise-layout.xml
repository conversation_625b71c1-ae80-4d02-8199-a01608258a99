<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_dispute_raise" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\layout_dispute_raise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/layout_dispute_raise_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="319" endOffset="51"/></Target><Target id="@+id/ivDisputeLogo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="12" startOffset="8" endLine="22" endOffset="13"/></Target><Target id="@+id/tvDisputeHeading" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="25" startOffset="8" endLine="35" endOffset="13"/></Target><Target id="@+id/cvMessageId" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="37" startOffset="8" endLine="84" endOffset="43"/></Target><Target id="@+id/tvMessageId" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="57" startOffset="16" endLine="65" endOffset="21"/></Target><Target id="@+id/etHashId" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="68" startOffset="16" endLine="78" endOffset="21"/></Target><Target id="@+id/cvSenderEmail" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="87" startOffset="8" endLine="134" endOffset="43"/></Target><Target id="@+id/tvTitleSenderEmail" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="107" startOffset="16" endLine="115" endOffset="21"/></Target><Target id="@+id/etBotMail" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="118" startOffset="16" endLine="128" endOffset="21"/></Target><Target id="@+id/llMainPending" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="137" startOffset="4" endLine="241" endOffset="50"/></Target><Target id="@+id/cvStatus" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="146" startOffset="8" endLine="191" endOffset="43"/></Target><Target id="@+id/tvStatusTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="165" startOffset="16" endLine="173" endOffset="21"/></Target><Target id="@+id/etBotStatus" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="176" startOffset="16" endLine="186" endOffset="21"/></Target><Target id="@+id/cvCounter" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="193" startOffset="8" endLine="238" endOffset="43"/></Target><Target id="@+id/tvTitleStatus" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="212" startOffset="16" endLine="220" endOffset="21"/></Target><Target id="@+id/etBotCounter" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="223" startOffset="16" endLine="233" endOffset="21"/></Target><Target id="@+id/cvReason" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="244" startOffset="4" endLine="290" endOffset="39"/></Target><Target id="@+id/tvTitleReason" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="264" startOffset="12" endLine="272" endOffset="17"/></Target><Target id="@+id/etBotReason" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="275" startOffset="12" endLine="285" endOffset="17"/></Target><Target id="@+id/btSubmit" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="293" startOffset="4" endLine="313" endOffset="9"/></Target></Targets></Layout>