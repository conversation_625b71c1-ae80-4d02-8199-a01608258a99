<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/main"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Fragments.ActivityGraphFragment">

<!--    <com.github.mikephil.charting.charts.PieChart
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/pieChart"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:paddingHorizontal="@dimen/_16sdp"
        android:paddingTop="@dimen/_16sdp"
        android:paddingBottom="@dimen/_24sdp">

        <!-- Header Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Header Title -->
                <TextView
                    android:id="@+id/tvHeader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 Email Activity Dashboard"
                    android:textSize="@dimen/_14sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_bold"
                    android:gravity="start" />

                <!-- Sub Title -->
                <TextView
                    android:id="@+id/tvSubTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Real-time email security monitoring and threat analysis"
                    android:textSize="@dimen/_10sdp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="@font/roboto_semi_regular"
                    android:gravity="start"
                    android:layout_marginTop="@dimen/_4sdp" />

                <!-- Status Indicator -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:background="@drawable/rounded_background"
                    android:backgroundTint="@color/success_light"
                    android:paddingHorizontal="@dimen/_8sdp"
                    android:paddingVertical="@dimen/_4sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🟢 System Active"
                        android:textSize="@dimen/_8sdp"
                        android:textColor="@color/success"
                        android:fontFamily="@font/roboto_semi_medium" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>


        <!-- Chart Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvChart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvHeader"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Chart Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📈 Email Distribution Analysis"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_8sdp" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pieChart"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_200sdp"
                    android:layout_marginVertical="@dimen/_8sdp" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Statistics Cards -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvStats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvChart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Stats Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 Quick Statistics"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Quick Stats: 3 cards -->
                <LinearLayout
                    android:id="@+id/summaryCards"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <!-- Dispute Mail -->
                    <androidx.cardview.widget.CardView
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_80sdp"
                        android:layout_margin="@dimen/_4sdp"
                        app:cardCornerRadius="@dimen/_8sdp"
                        app:cardElevation="@dimen/_2sdp"
                        app:cardBackgroundColor="@color/success_light">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="@dimen/_8sdp">

                            <TextView
                                android:id="@+id/tvDispute"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🛡️ 0"
                                android:textSize="@dimen/_14sdp"
                                android:textColor="@color/success"
                                android:fontFamily="@font/roboto_semi_bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Disputes"
                                android:textSize="@dimen/_9sdp"
                                android:textColor="@color/success"
                                android:fontFamily="@font/roboto_semi_regular"
                                android:layout_marginTop="@dimen/_2sdp" />

                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <!-- Spam Mail -->
                    <androidx.cardview.widget.CardView
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_80sdp"
                        android:layout_margin="@dimen/_4sdp"
                        app:cardCornerRadius="@dimen/_8sdp"
                        app:cardElevation="@dimen/_2sdp"
                        app:cardBackgroundColor="@color/warning_light">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="@dimen/_8sdp">

                            <TextView
                                android:id="@+id/tvSpam"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⚠️ 0"
                                android:textSize="@dimen/_14sdp"
                                android:textColor="@color/warning"
                                android:fontFamily="@font/roboto_semi_bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Spam Mails"
                                android:textSize="@dimen/_9sdp"
                                android:textColor="@color/warning"
                                android:fontFamily="@font/roboto_semi_regular"
                                android:layout_marginTop="@dimen/_2sdp" />

                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <!-- Process Mail -->
                    <androidx.cardview.widget.CardView
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_80sdp"
                        android:layout_margin="@dimen/_4sdp"
                        app:cardCornerRadius="@dimen/_8sdp"
                        app:cardElevation="@dimen/_2sdp"
                        app:cardBackgroundColor="@color/info_light">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="@dimen/_8sdp">

                            <TextView
                                android:id="@+id/tvProcessed"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📧 0"
                                android:textSize="@dimen/_14sdp"
                                android:textColor="@color/info"
                                android:fontFamily="@font/roboto_semi_bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Processed"
                                android:textSize="@dimen/_9sdp"
                                android:textColor="@color/info"
                                android:fontFamily="@font/roboto_semi_regular"
                                android:layout_marginTop="@dimen/_2sdp" />

                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Insights Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvInsights"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvStats"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Insights Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 Security Insights"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Insight Items -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/rounded_background"
                    android:backgroundTint="@color/surface"
                    android:padding="@dimen/_12sdp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📅 Last Scan: Today at 2:30 PM"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular"
                        android:layout_marginBottom="@dimen/_6sdp"
                        android:drawablePadding="@dimen/_8sdp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔍 Total Emails Scanned: 1,247"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular"
                        android:layout_marginBottom="@dimen/_6sdp"
                        android:drawablePadding="@dimen/_8sdp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🛡️ Protection Level: High Security"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/success"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:layout_marginBottom="@dimen/_6sdp"
                        android:drawablePadding="@dimen/_8sdp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🧠 AI Accuracy: 96.8% Detection Rate"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/info"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:drawablePadding="@dimen/_8sdp" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Performance Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvPerformance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvInsights"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Performance Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="⚡ System Performance"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Performance Grid -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Response Time -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:background="@drawable/rounded_background"
                        android:backgroundTint="@color/surface"
                        android:padding="@dimen/_12sdp"
                        android:layout_marginEnd="@dimen/_8sdp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="⏱️"
                            android:textSize="@dimen/_16sdp"
                            android:gravity="center"
                            android:layout_marginBottom="@dimen/_4sdp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="2s"
                            android:textSize="@dimen/_12sdp"
                            android:textColor="@color/text_primary"
                            android:fontFamily="@font/roboto_semi_bold"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Response Time"
                            android:textSize="@dimen/_8sdp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="@font/roboto_semi_regular"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/_2sdp" />

                    </LinearLayout>




                    <!-- Uptime -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:background="@drawable/rounded_background"
                        android:backgroundTint="@color/surface"
                        android:padding="@dimen/_12sdp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🔄"
                            android:textSize="@dimen/_16sdp"
                            android:gravity="center"
                            android:layout_marginBottom="@dimen/_4sdp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="99.9%"
                            android:textSize="@dimen/_12sdp"
                            android:textColor="@color/text_primary"
                            android:fontFamily="@font/roboto_semi_bold"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="System Uptime"
                            android:textSize="@dimen/_8sdp"
                            android:textColor="@color/text_secondary"
                            android:fontFamily="@font/roboto_semi_regular"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/_2sdp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>