<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".BottomSheet.RaiseBottomSheetFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/_10sdp"
        android:foreground="?attr/selectableItemBackground"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        >


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDisputeLogo"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_50sdp"
            android:src="@drawable/mail_raised"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:padding="@dimen/_10sdp"
            android:layout_margin="@dimen/_10sdp"
            />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDisputeHeading"
            app:layout_constraintTop_toBottomOf="@+id/ivDisputeLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/get_stared_raised_disputes_against"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textSize="@dimen/_12sdp"
            android:fontFamily="@font/roboto_semi_bold"
            />

        <androidx.cardview.widget.CardView
            android:id="@+id/cvMessageId"
            android:layout_marginTop="@dimen/_10sdp"
            app:layout_constraintTop_toBottomOf="@+id/tvDisputeHeading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            app:cardBackgroundColor="@color/light_grey"
            app:cardCornerRadius="@dimen/_10sdp"
            android:elevation="0dp"
            app:cardElevation="0dp"
            >

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_margin="@dimen/_10sdp"
                >

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMessageId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/message_id"
                    android:textSize="@dimen/_8sdp"
                    android:textColor="@color/dark_grey"
                    android:fontFamily="@font/roboto_semi_regular"
                    />


                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etMessageId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:text="123455453254325"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_10sdp"
                    android:focusable="false"
                    />

            </androidx.appcompat.widget.LinearLayoutCompat>



        </androidx.cardview.widget.CardView>


        <androidx.cardview.widget.CardView
            android:id="@+id/cvSenderEmail"
            android:layout_marginTop="@dimen/_10sdp"
            app:layout_constraintTop_toBottomOf="@+id/cvMessageId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            app:cardBackgroundColor="@color/light_grey"
            app:cardCornerRadius="@dimen/_10sdp"
            android:elevation="0dp"
            app:cardElevation="0dp"
            >

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_margin="@dimen/_10sdp"
                >

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitleSenderEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sender_email"
                    android:textSize="@dimen/_8sdp"
                    android:textColor="@color/dark_grey"
                    android:fontFamily="@font/roboto_semi_regular"
                    />


                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etSenderMail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:text="<EMAIL>"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_10sdp"
                    android:focusable="false"
                    />

            </androidx.appcompat.widget.LinearLayoutCompat>



        </androidx.cardview.widget.CardView>


        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llMainPending"
            app:layout_constraintTop_toBottomOf="@+id/cvSenderEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:baselineAligned="false"
            android:layout_margin="@dimen/_5sdp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cvStatus"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/light_grey"
                app:cardCornerRadius="@dimen/_10sdp"
                android:elevation="0dp"
                app:cardElevation="0dp"
                android:layout_margin="@dimen/_5sdp"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_margin="@dimen/_10sdp"
                    >

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvStatusTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/status"
                        android:textSize="@dimen/_8sdp"
                        android:textColor="@color/dark_grey"
                        android:fontFamily="@font/roboto_semi_regular"
                        />


                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:text="Pending"
                        android:focusable="false"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_10sdp"
                        />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cvCounter"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/light_grey"
                app:cardCornerRadius="@dimen/_10sdp"
                android:elevation="0dp"
                app:cardElevation="0dp"
                android:layout_margin="@dimen/_5sdp"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_margin="@dimen/_10sdp"
                    >

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvTitleStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/counter"
                        android:textSize="@dimen/_8sdp"
                        android:textColor="@color/dark_grey"
                        android:fontFamily="@font/roboto_semi_regular"
                        />


                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etCounter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:text="0 of 3"
                        android:focusable="false"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_10sdp"
                        />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.cardview.widget.CardView>


        </androidx.appcompat.widget.LinearLayoutCompat>


        <androidx.cardview.widget.CardView
            android:id="@+id/cvReason"
            android:layout_marginTop="@dimen/_10sdp"
            app:layout_constraintTop_toBottomOf="@+id/llMainPending"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            app:cardBackgroundColor="@color/light_grey"
            app:cardCornerRadius="@dimen/_10sdp"
            android:elevation="0dp"
            app:cardElevation="0dp"
            >

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_margin="@dimen/_10sdp"
                >

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitleReason"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/reason_of_raising_dispute"
                    android:textSize="@dimen/_8sdp"
                    android:textColor="@color/dark_grey"
                    android:fontFamily="@font/roboto_semi_regular"
                    />


                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etReason"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="Enter reason...."
                    android:padding="@dimen/_10sdp"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_10sdp"
                    />


            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.cardview.widget.CardView>


        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btSubmit"
            app:layout_constraintTop_toBottomOf="@+id/cvReason"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="5dp"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:elevation="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_20sdp"
            android:paddingStart="@dimen/_15sdp"
            android:paddingEnd="@dimen/_15sdp"
            android:textAllCaps="false"
            android:visibility="visible"
            android:background="@drawable/bg_button_grey_color"
            android:fontFamily="@font/roboto_semi_medium"
            android:textSize="@dimen/_12sdp"
            android:textColor="@color/white"
            android:text="@string/submit"
            android:layout_marginBottom="@dimen/_20sdp"
            />





    </androidx.constraintlayout.widget.ConstraintLayout>






</ScrollView>